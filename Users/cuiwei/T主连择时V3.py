# ==============================================================================
#                      T主连量化择时回测脚本 (本地运行版)
# ==============================================================================

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.font_manager
import seaborn as sns
import os

# ==============================================================================
# 0. 全局配置 (请确认文件路径正确)
# ==============================================================================

# 【请确认】您的Excel数据文件路径
# 根据您的描述，我已将路径设置为此。如果文件名不同，请在此处修改。
file_path = "/Users/<USER>/Desktop/T主连择时-最终版.xlsx" 

# 结果输出文件夹路径 (将创建在您的桌面)
output_folder = os.path.expanduser("~/Desktop/T主连量化择时结果")

# 回测区间
BACKTEST_START_DATE = '2020-01-05'
BACKTEST_END_DATE = '2025-08-09'


# ==============================================================================
# 1. 中文字体设置 (兼容macOS)
# ==============================================================================
def setup_chinese_font():
    """
    为matplotlib设置一个可用的中文字体，以解决macOS下的显示问题。
    """
    print("正在配置中文字体...")
    try:
        candidates = [
            "/System/Library/Fonts/PingFang.ttc",
            "PingFang SC", "Heiti TC", "Songti SC", # macOS 常见字体
            "Microsoft YaHei", # Windows 常见字体
            "/Library/Fonts/NotoSansSC-Regular.otf",
            "/System/Library/Fonts/STHeiti Medium.ttc",
        ]
        picked = None
        for font in candidates:
            try:
                # 优先按名称查找
                matplotlib.font_manager.FontProperties(font).get_name()
                picked = font
                break
            except Exception:
                # 如果是路径，则检查路径是否存在
                if os.path.exists(font):
                    matplotlib.font_manager.fontManager.addfont(font)
                    picked = matplotlib.font_manager.FontProperties(fname=font).get_name()
                    break

        if picked:
            plt.rcParams["font.sans-serif"] = [picked]
            plt.rcParams["axes.unicode_minus"] = False
            print(f"中文字体设置为: '{picked}'")
        else:
            print("警告：未找到推荐的中文字体，图表中的中文可能无法正常显示。")
            plt.rcParams["font.sans-serif"] = ["DejaVu Sans"]
            plt.rcParams["axes.unicode_minus"] = False

    except Exception as e:
        print(f"字体设置时发生错误: {e}")
        print("将使用默认字体。")


# ==============================================================================
# 2. 数据加载与预处理
# ==============================================================================
def load_and_prepare_data(excel_path, backtest_start, backtest_end):
    """
    加载并处理日频行情和周频指数数据。
    """
    print("\n正在加载和预处理数据...")
    if not os.path.exists(excel_path):
        print(f"错误：无法在以下路径找到文件: {excel_path}")
        print("请检查文件路径是否正确，然后重新运行脚本。")
        return None

    # 读取数据
    df_daily = pd.read_excel(excel_path, sheet_name='sheet1')
    df_weekly = pd.read_excel(excel_path, sheet_name='sheet2')

    # --- 处理日频数据 ---
    df_daily.rename(columns={
        '日期': 'date', '收盘价': 'close', '开盘价': 'open',
        '最高价': 'high', '最低价': 'low', '成交量': 'volume'
    }, inplace=True)
    df_daily['date'] = pd.to_datetime(df_daily['date'])
    df_daily.set_index('date', inplace=True)

    # --- 处理周频数据 ---
    df_weekly.rename(columns={
        '日期': 'date', '利率综合领先指数': 'leading_index'
    }, inplace=True)
    df_weekly['date'] = pd.to_datetime(df_weekly['date'])
    df_weekly.set_index('date', inplace=True)
    df_weekly.sort_index(inplace=True)

    # --- 合并数据 ---
    df_merged = pd.merge_asof(
        df_daily.sort_index(),
        df_weekly,
        left_index=True,
        right_index=True,
        direction='backward'
    )
    df_merged['leading_index'].fillna(method='ffill', inplace=True)

    # --- 计算基准收益 ---
    df_merged['daily_return'] = df_merged['close'].pct_change().fillna(0)
    
    # --- 截取回测区间 ---
    df_backtest = df_merged.loc[backtest_start:backtest_end].copy()
    
    # 重新计算回测区间的基准净值，使其从1开始
    df_backtest['benchmark_net_value'] = (1 + df_backtest['daily_return']).cumprod()
    
    print("数据加载和预处理完成。")
    return df_backtest

# ==============================================================================
# 3. 策略实现 (此部分与之前版本完全相同)
# ==============================================================================

def run_strategy_1(df):
    """策略1: 纯利率领先指数择时"""
    print("运行策略1: 纯利率领先指数择时...")
    df_strat = df.copy()
    
    window = 260 # 约52周
    rolling_mean = df_strat['leading_index'].rolling(window=window, min_periods=20).mean()
    rolling_std = df_strat['leading_index'].rolling(window=window, min_periods=20).std()
    df_strat['z_score'] = (df_strat['leading_index'] - rolling_mean) / rolling_std

    df_strat['position'] = 0
    df_strat.loc[df_strat['z_score'] > 1.5, 'position'] = -1 # 看跌
    df_strat.loc[df_strat['z_score'] < -1.5, 'position'] = 1  # 看涨
    df_strat['position'].fillna(0, inplace=True)

    df_strat['strategy_return'] = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'].fillna(0, inplace=True)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()
    
    return df_strat


def run_strategy_2(df):
    """策略2: 纯技术因子择时 (双均线)"""
    print("运行策略2: 纯技术因子择时...")
    df_strat = df.copy()

    df_strat['ma20'] = df_strat['close'].rolling(window=20).mean()
    df_strat['ma60'] = df_strat['close'].rolling(window=60).mean()

    df_strat['position'] = np.where(df_strat['ma20'] > df_strat['ma60'], 1, -1)
    df_strat['position'].fillna(0, inplace=True) 

    df_strat['strategy_return'] = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'].fillna(0, inplace=True)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()

    return df_strat


def run_strategy_3(df_s1, df_s2):
    """策略3: 利率领先指数 + 技术因子择时"""
    print("运行策略3: 利率领先指数 + 技术因子择时...")
    df_strat = df_s1.copy()
    
    macro_signal = df_s1['position']
    tech_signal = df_s2['position']

    conditions = [
        (macro_signal == 1) & (tech_signal == 1),
        (macro_signal == -1) & (tech_signal == -1)
    ]
    choices = [1, -1]
    df_strat['position'] = np.select(conditions, choices, default=0)

    df_strat['strategy_return'] = df_strat['position'].shift(1) * df_strat['daily_return']
    df_strat['strategy_return'].fillna(0, inplace=True)
    df_strat['strategy_net_value'] = (1 + df_strat['strategy_return']).cumprod()
    
    return df_strat


# ==============================================================================
# 4. 性能分析与报告生成 (此部分与之前版本完全相同)
# ==============================================================================
def calculate_performance_metrics(df, strategy_name):
    """计算详细的量化回测指标"""
    if df['strategy_net_value'].iloc[-1] == 1 and df['strategy_return'].sum() == 0:
        return pd.Series({ '年化收益率': 0, '年化波动率': 0, '夏普比率': 0, '最大回撤': 0, '卡玛比率': 0, '胜率': 0 }, name=strategy_name)

    total_days = len(df)
    trading_days_per_year = 252
    
    annual_return = (df['strategy_net_value'].iloc[-1]) ** (trading_days_per_year / total_days) - 1
    annual_volatility = df['strategy_return'].std() * np.sqrt(trading_days_per_year)
    sharpe_ratio = annual_return / annual_volatility if annual_volatility != 0 else 0
    
    df['cumulative_max'] = df['strategy_net_value'].cummax()
    df['drawdown'] = (df['strategy_net_value'] - df['cumulative_max']) / df['cumulative_max']
    max_drawdown = df['drawdown'].min()
    
    calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0
    
    trade_days = df[df['position'].shift(1) != 0]
    win_rate = (trade_days['strategy_return'] > 0).sum() / len(trade_days) if len(trade_days) > 0 else 0

    return pd.Series({
        '年化收益率': f"{annual_return:.2%}", '年化波动率': f"{annual_volatility:.2%}",
        '夏普比率': f"{sharpe_ratio:.2f}", '最大回撤': f"{max_drawdown:.2%}",
        '卡玛比率': f"{calmar_ratio:.2f}", '胜率': f"{win_rate:.2%}"
    }, name=strategy_name)


def generate_report(df, strategy_name, output_folder_path):
    """为单个策略生成所有图表和指标"""
    print(f"\n--- 正在为 '{strategy_name}' 生成报告 ---")
    
    # 图1: 累计净值与最大回撤
    fig, ax1 = plt.subplots(figsize=(16, 8))
    df['strategy_net_value'].plot(ax=ax1, label=f'{strategy_name}净值', color='crimson', lw=2)
    df['benchmark_net_value'].plot(ax=ax1, label='基准净值', color='royalblue', ls='--', lw=2)
    ax1.set_title(f'{strategy_name} vs. 基准: 累计净值与最大回撤', fontsize=18)
    ax1.set_ylabel('累计净值', fontsize=12)
    ax1.set_xlabel('日期', fontsize=12)
    ax1.grid(True, alpha=0.4)
    ax1.legend(loc='upper left')
    ax1.fill_between(df.index, df['strategy_net_value'], df['cumulative_max'], where=df['strategy_net_value'] < df['cumulative_max'], color='crimson', alpha=0.2)
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder_path, f"{strategy_name}_1_净值与回撤图.png"))
    plt.close(fig)

    # 图2: 月度收益率
    strat_monthly = df['strategy_return'].resample('M').apply(lambda r: (1 + r).prod() - 1)
    bench_monthly = df['daily_return'].resample('M').apply(lambda r: (1 + r).prod() - 1)
    monthly_returns = pd.DataFrame({f'{strategy_name}': strat_monthly, '基准': bench_monthly})
    fig, ax = plt.subplots(figsize=(16, 8))
    monthly_returns.plot(kind='bar', ax=ax, width=0.8)
    ax.set_title(f'{strategy_name} vs. 基准: 月度收益率分布', fontsize=18)
    ax.axhline(0, color='grey', linewidth=0.8)
    ax.xaxis.set_major_formatter(plt.FixedFormatter(monthly_returns.index.strftime('%Y-%m')))
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder_path, f"{strategy_name}_2_月度收益图.png"))
    plt.close(fig)

    # 图3: 超额收益
    df['excess_return'] = df['strategy_return'] - df['daily_return']
    df['cumulative_excess_return'] = (1 + df['excess_return']).cumprod() -1
    fig, ax = plt.subplots(figsize=(16, 8))
    df['cumulative_excess_return'].plot(ax=ax, color='green', lw=2)
    ax.set_title(f'{strategy_name} vs. 基准: 累计超额收益', fontsize=18)
    ax.grid(True, alpha=0.4)
    ax.axhline(0, color='grey', linewidth=0.8)
    plt.tight_layout()
    plt.savefig(os.path.join(output_folder_path, f"{strategy_name}_3_超额收益图.png"))
    plt.close(fig)
    print(f"'{strategy_name}' 的所有图表已保存。")

    metrics = calculate_performance_metrics(df, strategy_name)
    base_metrics = calculate_performance_metrics(df.rename(columns={'benchmark_net_value': 'strategy_net_value', 'daily_return': 'strategy_return'}), '基准')
    base_metrics['胜率'] = f"{(df['daily_return'] > 0).sum() / len(df):.2%}"
    
    return metrics, base_metrics

# ==============================================================================
# 5. 主执行流程
# ==============================================================================
def main():
    """主函数，串联所有流程"""
    print("脚本开始执行...")
    
    # 设置字体并创建输出文件夹
    setup_chinese_font()
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    print(f"结果将保存在您的桌面文件夹: {output_folder}")
    
    # 加载数据
    base_df = load_and_prepare_data(file_path, BACKTEST_START_DATE, BACKTEST_END_DATE)
    
    # 如果数据加载失败，则终止程序
    if base_df is None:
        return
        
    # 运行策略
    s1_df = run_strategy_1(base_df)
    s2_df = run_strategy_2(base_df)
    s3_df = run_strategy_3(s1_df, s2_df)
    
    # 生成报告
    s1_metrics, base_metrics = generate_report(s1_df, '策略1_纯利率指数择时', output_folder)
    s2_metrics, _ = generate_report(s2_df, '策略2_纯技术因子择时', output_folder)
    s3_metrics, _ = generate_report(s3_df, '策略3_利率指数加技术因子', output_folder)
    
    # 额外图表: 策略2 vs 策略3 净值对比
    print("\n--- 正在生成额外对比图 ---")
    fig, ax = plt.subplots(figsize=(16, 8))
    s2_df['strategy_net_value'].plot(ax=ax, label='策略2_纯技术因子择时', color='darkorange', lw=2)
    s3_df['strategy_net_value'].plot(ax=ax, label='策略3_利率指数加技术因子', color='purple', lw=2)
    base_df['benchmark_net_value'].plot(ax=ax, label='基准净值', color='royalblue', ls='--', lw=2)
    ax.set_title('策略2 vs 策略3: 净值对比', fontsize=18)
    ax.set_ylabel('累计净值')
    ax.grid(True, alpha=0.4)
    ax.legend(loc='upper left')
    plt.tight_layout()
    plot_path = os.path.join(output_folder, "对比图_策略2_vs_策略3.png")
    plt.savefig(plot_path)
    plt.close(fig)
    print(f"对比图已保存: {plot_path}")

    # 汇总所有指标到Excel
    all_metrics = pd.concat([base_metrics.to_frame().T, s1_metrics.to_frame().T, s2_metrics.to_frame().T, s3_metrics.to_frame().T])
    
    excel_output_path = os.path.join(output_folder, '量化回测结果汇总.xlsx')
    with pd.ExcelWriter(excel_output_path, engine='openpyxl') as writer:
        all_metrics.to_excel(writer, sheet_name='回测性能指标汇总')
        s1_df[['strategy_net_value', 'benchmark_net_value', 'position', 'z_score']].to_excel(writer, sheet_name='策略1详细数据')
        s2_df[['strategy_net_value', 'benchmark_net_value', 'position', 'ma20', 'ma60']].to_excel(writer, sheet_name='策略2详细数据')
        s3_df[['strategy_net_value', 'benchmark_net_value', 'position']].to_excel(writer, sheet_name='策略3详细数据')
    print(f"\n所有回测指标和详细数据已保存至Excel文件: {excel_output_path}")
    
    print("\n\n*** 所有任务执行完毕！ ***")

# 脚本执行入口
if __name__ == "__main__":
    main()